/*
 * Yendor Cats - Section Styles
 * Styling for all major page sections
 */

/* Section Color Alternation */
main > section:nth-of-type(odd) {
    background-color: var(--color-red); /* Dark red for odd sections */
}

main > section:nth-of-type(even) {
    background-color: var(--color-bright-red); /* Bright red for even sections */
}

/* Main content adjustments for full-height navbar */
main {
    margin-top: 0; /* Remove excessive margin */
    padding-top: 0; /* The navbar is already full height and fixed */
    background-color: var(--color-red);
    color: var(--text-primary);
    z-index: 1;
    position: relative;
}

/* Adjust padding for main content when navbar is scrolled */
.header.scrolled ~ main {
    padding-top: 100px; /* Increased from 80px to 100px */
}

/* Add top margin to all sections to prevent content from being cut off */
.section-padding {
    padding: var(--spacing-xxl) 0;
}

/* Add extra padding to the first section when navbar is collapsed */
.header.scrolled ~ main section:first-of-type {
    padding-top: calc(var(--spacing-xxl) + 40px); /* Increased from 20px to 40px */
}

/* Section headers */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.section-header h2 {
    display: inline-block;
    position: relative;
    margin-bottom: var(--spacing-md);
}

.section-header h2::after {
    content: '';
    display: block;
    width: 80px;
    height: 3px;
    background-color: var(--accent-primary);
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.section-header p {
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto var(--spacing-lg);
}

/* Hero Section */
.hero {
    position: relative;
    padding: var(--spacing-xxl) 0;
    background-color: var(--bg-secondary);
    overflow: hidden;
}

/* Adjust hero padding when navbar is collapsed */
.header.scrolled ~ main .hero {
    padding-top: calc(var(--spacing-xxl) + 50px); /* Increased from 30px to 50px */
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
}

.hero-content {
    position: relative;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    z-index: 2;
}

.hero h2 {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-lg);
}

.hero-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-md);
}

/* About Section */
.about {
    padding: var(--spacing-xxl) 0;
    background-color: var(--bg-primary);
}

.about-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

/* Images Sidebar */
.about-images-sidebar {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: center;
}

.about-image {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius-md);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.about-image img {
    transition: transform var(--transition-medium);
    display: block;
}

.about-image:hover img {
    transform: scale(1.05);
}

/* Ginger kitten - rotated horizontal image */
.ginger-kitten {
    width: 200px;
    height: 300px;
}

.ginger-kitten img {
    width: 300px;
    height: 200px;
    object-fit: cover;
    transform: rotate(-90deg);
    transform-origin: center center;
}

.ginger-kitten:hover img {
    transform: rotate(-90deg) scale(1.05);
}

/* Vertical kittens image */
.vertical-kittens {
    width: 200px;
    height: 280px;
}

.vertical-kittens img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* About text content */
.about-text {
    flex: 1;
}

.about-text-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.header-kitten-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.about-text h3 {
    margin: 0;
    color: var(--text-primary);
}

.about-text p:last-of-type {
    margin-bottom: var(--spacing-lg);
}

/* Images within text content */
.about-text .about-image {
    margin: var(--spacing-lg) 0;
    max-width: 100%;
}

.about-text .about-image img {
    width: 100%;
    height: auto;
}

/* Breeds Section */
.breeds {
    padding: var(--spacing-xxl) 0;
    background-color: var(--bg-secondary);
}

.breeds-container {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: var(--spacing-lg);
}

.breed-card {
    background-color: var(--bg-primary);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-medium);
}

.breed-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.breed-image {
    overflow: hidden;
    height: 250px;
}

.breed-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.breed-card:hover .breed-image img {
    transform: scale(1.1);
}

.breed-content {
    padding: var(--spacing-lg);
}

.breed-content h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--accent-primary);
}

.breed-content p {
    margin-bottom: var(--spacing-md);
}

.breed-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
    font-size: 0.9rem;
}

.breed-meta span {
    display: flex;
    align-items: center;
}

.breed-meta i {
    margin-right: var(--spacing-xs);
    color: var(--accent-primary);
}

.breed-link {
    display: inline-block;
    color: var(--accent-primary);
    font-weight: 600;
    position: relative;
    padding-right: 20px;
}

.breed-link::after {
    content: '→';
    position: absolute;
    right: 0;
    transition: transform var(--transition-fast);
}

.breed-link:hover::after {
    transform: translateX(5px);
}

/* Gallery Section */
.gallery {
    padding: var(--spacing-xxl) 0;
    background-color: var(--bg-primary);
}

/* Available Cats Section */
.available-cats {
    padding: var(--spacing-xxl) 0;
    background-color: var(--bg-secondary);
}

.cats-container {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: var(--spacing-lg);
}

.cat-card {
    background-color: var(--bg-primary);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-medium);
}

.cat-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.cat-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.cat-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.cat-card:hover .cat-image img {
    transform: scale(1.1);
}

.cat-status {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-available {
    background-color: var(--color-success);
    color: white;
}

.status-reserved {
    background-color: var(--color-warning);
    color: black;
}

.status-sold {
    background-color: var(--color-error);
    color: white;
}

.cat-content {
    padding: var(--spacing-lg);
}

.cat-content h3 {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-sm);
    color: var(--accent-primary);
}

.cat-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--accent-primary);
    margin-bottom: var(--spacing-md);
}

.cat-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.cat-detail {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
}

.cat-detail i {
    margin-right: var(--spacing-xs);
    color: var(--accent-primary);
}

.cat-content p {
    margin-bottom: var(--spacing-lg);
    font-size: 0.95rem;
    line-height: 1.6;
}

.cat-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Contact Section */
.contact {
    padding: var(--spacing-xxl) 0;
    background-color: var(--bg-primary);
}

/* Cat Details Page */
.cat-details-page {
    padding-top: 100px; /* Increased from 80px to 100px to account for header */
}

.cat-details-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.cat-details-header h1 {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-sm);
}

.cat-details-header p {
    color: var(--text-secondary);
    font-size: 1.2rem;
}

.cat-details-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.cat-gallery {
    border-radius: var(--border-radius-md);
    overflow: hidden;
}

.cat-info {
    padding: var(--spacing-lg);
    background-color: var(--primary-dark);
    color: var(--color-black);
    border-bottom-left-radius: var(--border-radius-md);
    border-bottom-right-radius: var(--border-radius-md);
}

.cat-info h3 {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    margin: 0 0 var(--spacing-sm);
    color: var(--color-red);
}

.cat-info p {
    margin: var(--spacing-xs) 0;
    color: var(--color-black);
}

.cat-attributes {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    background-color: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
}

.cat-attribute h3 {
    margin-bottom: var(--spacing-xs);
    color: var(--accent-primary);
    font-size: 1.1rem;
}

.cat-attribute p {
    font-size: 1rem;
}

.cat-description {
    background-color: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
}

.cat-description h2 {
    margin-bottom: var(--spacing-md);
    color: var(--accent-primary);
    font-size: 1.5rem;
}

.cat-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

/* Media Queries */
@media (min-width: 768px) {
    .hero h2 {
        font-size: 3.5rem;
    }
    
    .about-content {
        flex-direction: row;
        align-items: flex-start;
        gap: var(--spacing-xxl);
    }

    .about-images-sidebar {
        flex-shrink: 0;
        width: 220px;
        position: sticky;
        top: var(--spacing-xl);
    }

    .about-text {
        flex: 1;
        min-width: 0; /* Allows text to wrap properly */
    }
    
    .introduction-content {
        flex-direction: row;
    }
    
    .breeds-container {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .cats-container {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .cat-details-header h1 {
        font-size: 2.5rem;
    }
    
    .cat-actions {
        flex-direction: row;
    }
}

@media (min-width: 992px) {
    .breeds-container {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .cats-container {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .cat-details-grid {
        grid-template-columns: 1fr 1fr;
    }
    
    .cat-attributes {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Introduction Section */
.introduction {
    padding: var(--spacing-xxl) 0;
    background-color: var(--bg-secondary);
}

.introduction-content {
    display: flex;
    flex-direction: row;
    gap: var(--spacing-xl);
    align-items: flex-start;
}

.intro-text {
    flex: 1;
}

.intro-text p {
    margin-bottom: var(--spacing-md);
}

.intro-text p:last-of-type {
    margin-bottom: var(--spacing-lg);
}

.intro-image {
    flex: 0 0 25%;
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius-md);
}

.intro-image img {
    width: 100%;
    height: auto;
    transition: transform var(--transition-medium);
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-md);
    object-fit: cover;
}

.intro-image:hover img {
    transform: scale(1.05);
}

/* Mobile responsive */
@media (max-width: 768px) {
    .introduction-content {
        flex-direction: column;
    }
}

.history-link {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--accent-primary);
    color: var(--text-navbar);
    font-weight: 600;
    border-radius: var(--border-radius-sm);
    text-decoration: none;
    transition: background-color var(--transition-fast);
}

.history-link:hover {
    background-color: var(--accent-secondary);
}

/* Cat Categories Section */
.cat-categories {
    background-color: transparent !important;
    padding: 0;
}

/* Individual Category Sections */
.category-section {
    padding: var(--section-padding);
    background-color: var(--bg-light);
}

.category-section h2 {
    text-align: center;
    margin-bottom: 0.5rem;
}

.category-section .section-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 1.5rem;
    color: var(--text-secondary);
}

/* Sort buttons container */
.sort-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    margin-bottom: 1.5rem;
    gap: 10px;
}

/* Cat carousel container styling */
.category-section .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.cat-carousel {
    display: flex;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    gap: var(--spacing-lg);
    padding: 0;
    margin: 0 auto;
    width: 100%;
}

.cat-card {
    flex: 0 0 300px;
    scroll-snap-align: start;
    background: white;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: transform var(--transition-medium);
}

.cat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.cat-image {
    height: 250px;
    overflow: hidden;
}

.cat-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.cat-card:hover .cat-image img {
    transform: scale(1.05);
}

.cat-info {
    padding: var(--spacing-lg);
    background-color: white;
    color: var(--color-black);
    border-bottom-left-radius: var(--border-radius-md);
    border-bottom-right-radius: var(--border-radius-md);
}

.cat-info h3 {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    margin: 0 0 var(--spacing-sm);
    color: var(--color-red);
}

.cat-info p {
    margin: var(--spacing-xs) 0;
    color: var(--color-black);
}

/* Carousel Navigation Buttons */
.carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background-color: var(--color-gold);
    color: white;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    cursor: pointer;
    z-index: 10;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-md);
}

.carousel-nav:hover {
    background-color: var(--accent-primary);
    transform: translateY(-50%) scale(1.1);
}

.carousel-nav.prev {
    left: 5px;
}

.carousel-nav.next {
    right: 5px;
}

/* Make buttons bigger on larger screens */
@media (min-width: 768px) {
    .carousel-nav {
        width: 45px;
        height: 45px;
        font-size: 1.8rem;
    }
    
    .carousel-nav.prev {
        left: 10px;
    }
    
    .carousel-nav.next {
        right: 10px;
    }
}

/* Popup Overlay */
.popup-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    overflow-y: auto;
    padding: 0;
    justify-content: center;
    align-items: center;
}

.popup-content {
    background-color: var(--color-red);
    border-radius: var(--border-radius-md);
    max-width: 900px;
    width: 90%;
    margin: calc(var(--spacing-xl) + 80px) auto var(--spacing-xl);
    padding: var(--spacing-xl);
    position: relative;
    box-shadow: var(--shadow-lg);
    max-height: 90vh;
    overflow-y: auto;
    color: white;
}

.close-popup {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    font-size: 1.8rem;
    line-height: 1;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--accent-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    z-index: 1;
    border: none;
    outline: none;
}

.close-popup:hover {
    background-color: var(--accent-secondary);
    transform: scale(1.1);
}

.popup-content h2 {
    color: white;
    margin-bottom: var(--spacing-lg);
    font-size: 2rem;
    padding-right: 40px; /* Make room for close button */
}

.popup-content h3 {
    color: white;
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    font-size: 1.5rem;
}

.popup-content p {
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
}

.popup-images {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.popup-images img {
    max-width: 100%;
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-md);
}

@media (min-width: 768px) {
    .popup-images img {
        max-width: calc(50% - var(--spacing-md) / 2);
    }
}

/* Sorting controls */
.sort-buttons {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm);
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-sm);
}

.sort-buttons span {
    font-weight: 600;
    margin-right: var(--spacing-sm);
    color: var(--color-white);
}

.sort-button {
    background-color: var(--bg-dark);
    color: var(--color-black);
    border: none;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.sort-button:hover {
    background-color: var(--color-gold);
    color: var(--color-white);
}

.sort-button.active {
    background-color: var(--color-red);
    color: var(--color-white);
}

@media (max-width: 767px) {
    .sort-buttons {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .sort-ssuttons span {
        margin-bottom: var(--spacing-xs);
    }
    
    .sort-button {
        width: 100%;
        text-align: center;
        margin-bottom: var(--spacing-xs);
    }
}
